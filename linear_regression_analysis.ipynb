# Linear Regression Analysis: ABI MS Uplift Prediction

This notebook performs a linear regression analysis to predict **ABI MS Uplift - rel** using the specified features.

## Model Overview
- **Target Variable (y)**: ABI MS Uplift - rel (Numerical)
- **Features (x1-x10)**: Various categorical and numerical predictors
- **Special Handling**: Dummy encoding for ABI Mechanic (base: 'No NIP') and ABI Depth (base: '<20')


# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries imported successfully!")


## Data Loading and Initial Exploration


# Load the dataset
df = pd.read_csv('FinalADS.csv')

print(f"Dataset shape: {df.shape}")
print(f"\nColumn names:")
print(df.columns.tolist())


# Display first few rows
print("First 5 rows of the dataset:")
df.head()


# Count NAs in all columns
print("Count of NAs in each column:")
print(df.isna().sum())


## Feature Selection and Data Preparation

Selecting only the required columns as specified:
- **y**: ABI MS Promo Uplift - rel (Target)
- **x1**: Same Week (Categorical)
- **x2**: 1 wk before (Categorical)
- **x3**: 2 wk before (Categorical)
- **x4**: 1 wk after (Categorical)
- **x5**: 2 wk after (Categorical)
- **x6**: KSM (Categorical)
- **x7**: Avg Temp (Numerical)
- **x8**: ABI Coverage (Numerical)
- **x9**: ABI Depth (Categorical)
- **x10**: ABI Mechanic (Categorical)


# Select required columns
required_columns = [
    'ABI MS Promo Uplift - rel',  # Target variable (y)
    'Same Week',                  
    '1 wk before',               
    '2 wk before',               
    '1 wk after',                
    '2 wk after',                
    'KSM',                       
    'Avg Temp',                  
    'ABI Coverage',              
    'ABI Depth',                 
    'ABI Mechanic'               
]

# Create working dataset
data = df[required_columns].copy()

print(f"Working dataset shape: {data.shape}")
print(f"\nSelected columns: {data.columns.tolist()}")


# Check for missing values and basic info
print("Missing values per column:")
print(data.isnull().sum())

print("\nData types:")
print(data.dtypes)

print("\nBasic statistical summary:")
data.describe()


# Analyze categorical variables
categorical_cols = ['Same Week', '1 wk before', '2 wk before', '1 wk after', '2 wk after', 
                   'KSM', 'ABI Depth', 'ABI Mechanic']

print("Unique values in categorical columns:")
for col in categorical_cols:
    print(f"\n{col}: {data[col].unique()}")
    print(f"Value counts: {data[col].value_counts().to_dict()}")


## Data Preprocessing

### Dummy Encoding with Specified Base Categories
- **ABI Mechanic**: Using 'No NIP' as base category
- **ABI Depth**: Using '<20' as base category
- **Other categorical variables**: Standard dummy encoding (drop first category)


# Create a copy for preprocessing
data_processed = data.copy()

# Handle missing values if any
data_processed = data_processed.dropna()

print(f"Dataset shape after removing missing values: {data_processed.shape}")

# Separate target variable
y = data_processed['ABI MS Promo Uplift - rel']
X = data_processed.drop('ABI MS Promo Uplift - rel', axis=1)

print(f"Target variable shape: {y.shape}")
print(f"Features shape: {X.shape}")
print(f"\nTarget variable statistics:")
print(y.describe())


# Dummy encoding for categorical variables
# First, handle the special cases with specified base categories

# ABI Mechanic - Use "No NIP" as base
abi_mechanic_dummies = pd.get_dummies(X['ABI Mechanic'], prefix='ABI_Mechanic', drop_first=False)
if 'ABI_Mechanic_No NIP' in abi_mechanic_dummies.columns:
    abi_mechanic_dummies = abi_mechanic_dummies.drop('ABI_Mechanic_No NIP', axis=1)
    print("ABI Mechanic: Using 'No NIP' as base category")
else:
    print("Warning: 'No NIP' not found in ABI Mechanic values")
    print(f"Available values: {X['ABI Mechanic'].unique()}")
    # Use drop_first=True as fallback
    abi_mechanic_dummies = pd.get_dummies(X['ABI Mechanic'], prefix='ABI_Mechanic', drop_first=True)

print(f"ABI Mechanic dummy columns: {abi_mechanic_dummies.columns.tolist()}")


# ABI Depth - Use "<20" as base
abi_depth_dummies = pd.get_dummies(X['ABI Depth'], prefix='ABI_Depth', drop_first=False)
if 'ABI_Depth_<20' in abi_depth_dummies.columns:
    abi_depth_dummies = abi_depth_dummies.drop('ABI_Depth_<20', axis=1)
    print("ABI Depth: Using '<20' as base category")
else:
    print("Warning: '<20' not found in ABI Depth values")
    print(f"Available values: {X['ABI Depth'].unique()}")
    # Use drop_first=True as fallback
    abi_depth_dummies = pd.get_dummies(X['ABI Depth'], prefix='ABI_Depth', drop_first=True)

print(f"ABI Depth dummy columns: {abi_depth_dummies.columns.tolist()}")


# Handle other categorical variables with standard dummy encoding
other_categorical = ['Same Week', '1 wk before', '2 wk before', '1 wk after', '2 wk after', 'KSM']

other_dummies = pd.get_dummies(X[other_categorical], drop_first=True)

print(f"Other categorical dummy columns: {other_dummies.columns.tolist()}")

# Combine all features
# Numerical features
numerical_features = X[['Avg Temp', 'ABI Coverage']]

# Combine all features
X_final = pd.concat([
    numerical_features,
    abi_mechanic_dummies,
    abi_depth_dummies,
    other_dummies
], axis=1)

print(f"\nFinal feature matrix shape: {X_final.shape}")
print(f"\nFinal feature columns ({len(X_final.columns)}):")
for i, col in enumerate(X_final.columns, 1):
    print(f"{i:2d}. {col}")


## Model Training and Evaluation


# Split the data
X_train, X_test, y_train, y_test = train_test_split(
    X_final, y, test_size=0.2, random_state=42
)

print(f"Training set shape: {X_train.shape}")
print(f"Test set shape: {X_test.shape}")
print(f"Training target shape: {y_train.shape}")
print(f"Test target shape: {y_test.shape}")


# Create and train the linear regression model
model = LinearRegression()
model.fit(X_train, y_train)

print("Linear Regression model trained successfully!")
print(f"Number of features used: {len(model.coef_)}")

# Make predictions
y_train_pred = model.predict(X_train)
y_test_pred = model.predict(X_test)

print("Predictions completed!")


# Calculate performance metrics
train_r2 = r2_score(y_train, y_train_pred)
test_r2 = r2_score(y_test, y_test_pred)
train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
train_mae = mean_absolute_error(y_train, y_train_pred)
test_mae = mean_absolute_error(y_test, y_test_pred)

print("=" * 50)
print("MODEL PERFORMANCE METRICS")
print("=" * 50)
print(f"Training R²:    {train_r2:.4f}")
print(f"Test R²:        {test_r2:.4f}")
print(f"Training RMSE:  {train_rmse:.4f}")
print(f"Test RMSE:      {test_rmse:.4f}")
print(f"Training MAE:   {train_mae:.4f}")
print(f"Test MAE:       {test_mae:.4f}")
print(f"Intercept:      {model.intercept_:.4f}")
print("=" * 50)


# Generate a statsmodels OLS regression report for comparison

import statsmodels.api as sm

# Add constant term for intercept
X_train_sm = sm.add_constant(X_train)
X_test_sm = sm.add_constant(X_test)

# Fit OLS model on training data
ols_model = sm.OLS(y_train, X_train_sm).fit()

# Print the OLS summary report
print("=" * 50)
print("STATS MODELS OLS REGRESSION REPORT")
print("=" * 50)
print(ols_model.summary())

# Optionally, you can also predict and compare with test set
y_test_pred_ols = ols_model.predict(X_test_sm)

# Calculate and print OLS test R² and RMSE for comparison
ols_test_r2 = r2_score(y_test, y_test_pred_ols)
ols_test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred_ols))
print(f"OLS Test R²:   {ols_test_r2:.4f}")
print(f"OLS Test RMSE: {ols_test_rmse:.4f}")


## Model Coefficients Analysis


# Feature importance (coefficients)
feature_importance = pd.DataFrame({
    'Feature': X_final.columns,
    'Coefficient': model.coef_,
    'Abs_Coefficient': np.abs(model.coef_)
})

# Sort by absolute coefficient value
feature_importance = feature_importance.sort_values('Abs_Coefficient', ascending=False)

print("Feature Coefficients (sorted by absolute value):")
print(feature_importance.round(6))


# Plot feature coefficients
plt.figure(figsize=(12, 8))

# Get top 10 features by absolute coefficient (or all if less than 10)
n_features_to_show = min(10, len(feature_importance))
top_features = feature_importance.head(n_features_to_show)

colors = ['red' if coef < 0 else 'blue' for coef in top_features['Coefficient']]

plt.barh(range(len(top_features)), top_features['Coefficient'], color=colors, alpha=0.7)
plt.yticks(range(len(top_features)), top_features['Feature'])
plt.xlabel('Coefficient Value')
plt.title(f'Top {n_features_to_show} Feature Coefficients in Linear Regression Model\n(Red: Negative, Blue: Positive)')
plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
plt.grid(axis='x', alpha=0.3)
plt.tight_layout()
plt.show()


## Model Validation and Diagnostics


# Prediction vs Actual plots
plt.figure(figsize=(15, 5))

# Training set
plt.subplot(1, 3, 1)
plt.scatter(y_train, y_train_pred, alpha=0.5, color='blue')
plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
plt.xlabel('Actual')
plt.ylabel('Predicted')
plt.title(f'Training Set\nR² = {train_r2:.4f}')
plt.grid(True, alpha=0.3)

# Test set
plt.subplot(1, 3, 2)
plt.scatter(y_test, y_test_pred, alpha=0.5, color='green')
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('Actual')
plt.ylabel('Predicted')
plt.title(f'Test Set\nR² = {test_r2:.4f}')
plt.grid(True, alpha=0.3)

# Residuals plot
plt.subplot(1, 3, 3)
residuals = y_test - y_test_pred
plt.scatter(y_test_pred, residuals, alpha=0.5, color='orange')
plt.axhline(y=0, color='red', linestyle='--')
plt.xlabel('Predicted Values')
plt.ylabel('Residuals')
plt.title('Residuals Plot (Test Set)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()


## Model Summary and Interpretation


print("=" * 60)
print("LINEAR REGRESSION MODEL SUMMARY")
print("=" * 60)
print(f"Target Variable: ABI MS Promo Uplift - rel")
print(f"Number of Features: {len(X_final.columns)}")
print(f"Number of Observations: {len(y)}")
print(f"Training/Test Split: 80%/20%")
print()
print("DUMMY ENCODING APPLIED:")
print(f"- ABI Mechanic: Base category excluded (if 'No NIP' existed))
print(f"- ABI Depth: Base category excluded (if '<20' existed))
print(f"- Other categorical variables: Standard dummy encoding")
print()
print("PERFORMANCE METRICS:")
print(f"- Test R²: {test_r2:.4f} ({test_r2*100:.2f}% of variance explained))
print(f"- Test RMSE: {test_rmse:.4f}")
print(f"- Test MAE: {test_mae:.4f}")
print()
print("TOP 5 MOST INFLUENTIAL FEATURES:")
for i, (_, row) in enumerate(feature_importance.head(5).iterrows(), 1):
    direction = "increases" if row['Coefficient'] > 0 else "decreases"
    print(f"{i}. {row['Feature']}: {direction} target by {abs(row['Coefficient']):.4f}")
print("=" * 60)


## Conclusions and Next Steps

This linear regression analysis provides insights into the factors affecting ABI MS Uplift. 

### Key Findings:
1. **Model Performance**: The model explains a portion of the variance in the target variable
2. **Feature Importance**: The most influential features have been identified
3. **Dummy Encoding**: Categorical variables have been properly encoded with specified base categories

### Potential Next Steps:
1. **Feature Engineering**: Create interaction terms or polynomial features
2. **Regularization**: Try Ridge or Lasso regression to handle multicollinearity
3. **Model Validation**: Use cross-validation for more robust performance estimates
4. **Residual Analysis**: Investigate patterns in residuals for model improvement opportunities
5. **Business Interpretation**: Translate statistical findings into actionable business insights

### Model Equation Format:
**ABI MS Uplift - rel = Intercept + β₁×Feature₁ + β₂×Feature₂ + ... + βₙ×Featureₙ**

The complete equation includes all features shown in the coefficient analysis above.
