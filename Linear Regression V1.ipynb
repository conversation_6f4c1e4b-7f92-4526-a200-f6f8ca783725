{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Linear Regression Analysis - Iteration 1\n", "\n", "This notebook performs linear regression analysis on promotional uplift data using the latest ADS dataset.\n", "\n", "## Analysis Overview\n", "- **Target Variable**: ABI MS Promo Uplift - rel\n", "- **Features**: ABI Coverage, Same Week, 1 week after, 2 week after, 1 week before, 2 week before, Avg Temp, KSM, ABI Promo PTC vs Base, ABI Promo PTC/HL, W_W_Distribution\n", "- **Data Source**: latest_ads.csv\n", "- **Analysis Type**: OLS Regression with comprehensive statistical output\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1b88ac49-1b3b-452a-b4ca-c6b8d3f13183", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import statsmodels.api as sm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully for Iteration 1 analysis!\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9e96b3f4-165c-4a0f-a43d-5d5a7ba700a6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Load the dataset from CSV file\n", "df = pd.read_csv('latest_ads.csv')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Column names: {df.columns.tolist()}\")\n", "print(f\"\\nFirst few rows:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cc268065-6c33-40be-bb0a-e54d3a760bfb", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Define the required columns for Iteration 1 analysis\n", "required_columns = [\n", "    'ABI MS Promo Uplift - rel',  # Target variable\n", "    'ABI Coverage',\n", "    'Same Week',\n", "    '1 wk after',\n", "    '2 wk after', \n", "    '1 wk before',\n", "    '2 wk before',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI Promo PTC vs Base',\n", "    'ABI Promo PTC/HL',\n", "    'ABI Base W_Distribution'  # This maps to W_W_Distribution from your request\n", "]\n", "\n", "# Check which columns are available in the dataset\n", "available_columns = [col for col in required_columns if col in df.columns]\n", "missing_columns = [col for col in required_columns if col not in df.columns]\n", "\n", "print(f\"Available columns: {available_columns}\")\n", "print(f\"Missing columns: {missing_columns}\")\n", "\n", "# Create working dataset with available columns\n", "data = df[available_columns].copy()\n", "\n", "print(f\"\\nWorking dataset shape: {data.shape}\")\n", "print(f\"Selected columns: {data.columns.tolist()}\")\n", "\n", "# Display basic statistics\n", "print(f\"\\nBasic dataset info:\")\n", "print(data.info())\n", "print(f\"\\nTarget variable statistics:\")\n", "if 'ABI MS Promo Uplift - rel' in data.columns:\n", "    print(data['ABI MS Promo Uplift - rel'].describe())\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cfb4e1a8-eadd-44bf-893a-161fae803297", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"<PERSON>lo\", \"Monaco\", \"Consolas\", \"Ubuntu Mono\", \"Source Code Pro\", monospace;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">Running outlier removal for linear regression data\n", "==================================================\n", "Outlier Removal Process\n", "==================================================\n", "Input DataFrame: 846 records\n", "Columns: [&#39;ABI MS Promo Uplift - rel&#39;, &#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;, &#39;Avg Temp&#39;, &#39;ABI Coverage&#39;, &#39;ABI Depth&#39;, &#39;ABI Mechanic&#39;]\n", "Using uplift column: &#39;ABI MS Promo Uplift - rel&#39;\n", "\n", "Uplift Statistics:\n", "  Count: 846\n", "  Mean: 6.00%\n", "  Median: 2.51%\n", "  Std: 31.16%\n", "  Min: 0.37%\n", "  Max: 712.88%\n", "  95th percentile: 9.70%\n", "  99th percentile: 101.92%\n", "\n", "Identifying outliers with ABI MS Promo Uplift - rel &gt; 50.0%...\n", "Found 9 extreme outliers\n", "\n", "Outlier Analysis (Uplift &gt; 50.0%):\n", "Outlier 1:\n", "  Uplift: 712.88%\n", "  ABI Coverage: 0.978\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: &lt;20%\n", "  Avg Temp: 14.3\n", "Outlier 2:\n", "  Uplift: 309.23%\n", "  ABI Coverage: 0.942\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 14.7\n", "Outlier 3:\n", "  Uplift: 309.23%\n", "  ABI Coverage: 0.942\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 14.7\n", "Outlier 4:\n", "  Uplift: 149.19%\n", "  ABI Coverage: 0.985\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 4.4\n", "Outlier 5:\n", "  Uplift: 149.19%\n", "  ABI Coverage: 0.985\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 4.4\n", "Outlier 6:\n", "  Uplift: 149.19%\n", "  ABI Coverage: 0.985\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 4.4\n", "Outlier 7:\n", "  Uplift: 149.19%\n", "  ABI Coverage: 0.985\n", "  ABI Mechanic: LV\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 4.4\n", "Outlier 8:\n", "  Uplift: 144.86%\n", "  ABI Coverage: 0.777\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 14.7\n", "Outlier 9:\n", "  Uplift: 144.86%\n", "  ABI Coverage: 0.777\n", "  ABI Mechanic: Immediate\n", "  ABI Depth: 26%-30%\n", "  Avg Temp: 14.7\n", "\n", "Removing 9 extreme outliers from dataset...\n", "Cleaned dataset: 837 records (removed 9 outliers)\n", "\n", "Summary:\n", "Original dataset: 846 records\n", "After removing missing values: 846 records\n", "Extreme outliers found: 9 records\n", "Final cleaned dataset: 837 records\n", "\n", "Uplift statistics (after cleaning):\n", "  Mean: 3.41%\n", "  Median: 2.45%\n", "  Std: 3.55%\n", "  Max: 49.45%\n", "\n", "Outlier removal completed.\n", "Cleaned data shape: (837, 11)\n", "Removed 9 outliers\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">Running outlier removal for linear regression data\n==================================================\nOutlier Removal Process\n==================================================\nInput DataFrame: 846 records\nColumns: [&#39;ABI MS Promo Uplift - rel&#39;, &#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;, &#39;Avg Temp&#39;, &#39;ABI Coverage&#39;, &#39;ABI Depth&#39;, &#39;ABI Mechanic&#39;]\nUsing uplift column: &#39;ABI MS Promo Uplift - rel&#39;\n\nUplift Statistics:\n  Count: 846\n  Mean: 6.00%\n  Median: 2.51%\n  Std: 31.16%\n  Min: 0.37%\n  Max: 712.88%\n  95th percentile: 9.70%\n  99th percentile: 101.92%\n\nIdentifying outliers with ABI MS Promo Uplift - rel &gt; 50.0%...\nFound 9 extreme outliers\n\nOutlier Analysis (Uplift &gt; 50.0%):\nOutlier 1:\n  Uplift: 712.88%\n  ABI Coverage: 0.978\n  ABI Mechanic: Immediate\n  ABI Depth: &lt;20%\n  Avg Temp: 14.3\nOutlier 2:\n  Uplift: 309.23%\n  ABI Coverage: 0.942\n  ABI Mechanic: Immediate\n  ABI Depth: 26%-30%\n  Avg Temp: 14.7\nOutlier 3:\n  Uplift: 309.23%\n  ABI Coverage: 0.942\n  ABI Mechanic: Immediate\n  ABI Depth: 26%-30%\n  Avg Temp: 14.7\nOutlier 4:\n  Uplift: 149.19%\n  ABI Coverage: 0.985\n  ABI Mechanic: LV\n  ABI Depth: 26%-30%\n  Avg Temp: 4.4\nOutlier 5:\n  Uplift: 149.19%\n  ABI Coverage: 0.985\n  ABI Mechanic: LV\n  ABI Depth: 26%-30%\n  Avg Temp: 4.4\nOutlier 6:\n  Uplift: 149.19%\n  ABI Coverage: 0.985\n  ABI Mechanic: LV\n  ABI Depth: 26%-30%\n  Avg Temp: 4.4\nOutlier 7:\n  Uplift: 149.19%\n  ABI Coverage: 0.985\n  ABI Mechanic: LV\n  ABI Depth: 26%-30%\n  Avg Temp: 4.4\nOutlier 8:\n  Uplift: 144.86%\n  ABI Coverage: 0.777\n  ABI Mechanic: Immediate\n  ABI Depth: 26%-30%\n  Avg Temp: 14.7\nOutlier 9:\n  Uplift: 144.86%\n  ABI Coverage: 0.777\n  ABI Mechanic: Immediate\n  ABI Depth: 26%-30%\n  Avg Temp: 14.7\n\nRemoving 9 extreme outliers from dataset...\nCleaned dataset: 837 records (removed 9 outliers)\n\nSummary:\nOriginal dataset: 846 records\nAfter removing missing values: 846 records\nExtreme outliers found: 9 records\nFinal cleaned dataset: 837 records\n\nUplift statistics (after cleaning):\n  Mean: 3.41%\n  Median: 2.45%\n  Std: 3.55%\n  Max: 49.45%\n\nOutlier removal completed.\nCleaned data shape: (837, 11)\nRemoved 9 outliers\n</div>", "datasetInfos": [], "metadata": {}, "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["def remove_extreme_uplift_outliers(df, outlier_threshold=50.0):\n", "    \"\"\"\n", "    Remove extreme uplift outliers from DataFrame.\n", "    \"\"\"\n", "    print(\"Outlier Removal Process\")\n", "    print(\"=\" * 50)\n", "\n", "    if df is None or len(df) == 0:\n", "        print(\"Error: Provided DataFrame is empty or None!\")\n", "        return None, None\n", "\n", "    print(f\"Input DataFrame: {len(df)} records\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "\n", "    uplift_column = 'ABI MS Promo Uplift - rel'\n", "    if uplift_column not in df.columns:\n", "        print(f\"Error: Could not find uplift column '{uplift_column}'\")\n", "        return None, None\n", "\n", "    print(f\"Using uplift column: '{uplift_column}'\")\n", "\n", "    df_copy = df.copy()\n", "    df_copy[uplift_column] = pd.to_numeric(df_copy[uplift_column], errors='coerce')\n", "\n", "    original_count = len(df_copy)\n", "    df_copy = df_copy.dropna(subset=[uplift_column])\n", "    if len(df_copy) < original_count:\n", "        print(f\"Removed {original_count - len(df_copy)} rows with missing uplift values\")\n", "\n", "    print(f\"\\nUplift Statistics:\")\n", "    print(f\"  Count: {len(df_copy)}\")\n", "    print(f\"  Mean: {df_copy[uplift_column].mean():.2f}%\")\n", "    print(f\"  Median: {df_copy[uplift_column].median():.2f}%\")\n", "    print(f\"  Std: {df_copy[uplift_column].std():.2f}%\")\n", "    print(f\"  Min: {df_copy[uplift_column].min():.2f}%\")\n", "    print(f\"  Max: {df_copy[uplift_column].max():.2f}%\")\n", "    print(f\"  95th percentile: {df_copy[uplift_column].quantile(0.95):.2f}%\")\n", "    print(f\"  99th percentile: {df_copy[uplift_column].quantile(0.99):.2f}%\")\n", "\n", "    print(f\"\\nIdentifying outliers with {uplift_column} > {outlier_threshold}%...\")\n", "    extreme_outliers = df_copy[df_copy[uplift_column] > outlier_threshold].copy()\n", "\n", "    if len(extreme_outliers) == 0:\n", "        print(f\"No outliers found with uplift > {outlier_threshold}%\")\n", "        return df_copy, pd.DataFrame()\n", "\n", "    print(f\"Found {len(extreme_outliers)} extreme outliers\")\n", "\n", "    print(f\"\\nOutlier Analysis (Uplift > {outlier_threshold}%):\")\n", "    outlier_display = extreme_outliers.sort_values(uplift_column, ascending=False)\n", "    for idx, (_, row) in enumerate(outlier_display.head(10).iterrows(), 1):\n", "        print(f\"Outlier {idx}:\")\n", "        print(f\"  Uplift: {row[uplift_column]:.2f}%\")\n", "        print(f\"  ABI Coverage: {row['ABI Coverage']:.3f}\")\n", "        print(f\"  ABI Mechanic: {row['ABI Mechanic']}\")\n", "        print(f\"  ABI Depth: {row['ABI Depth']}\")\n", "        print(f\"  Avg Temp: {row['Avg Temp']:.1f}\")\n", "\n", "    print(f\"\\nRemoving {len(extreme_outliers)} extreme outliers from dataset...\")\n", "    cleaned_df = df_copy.drop(extreme_outliers.index).copy()\n", "    print(f\"Cleaned dataset: {len(cleaned_df)} records (removed {len(extreme_outliers)} outliers)\")\n", "\n", "    print(f\"\\nSummary:\")\n", "    print(f\"Original dataset: {original_count} records\")\n", "    print(f\"After removing missing values: {len(df_copy)} records\")\n", "    print(f\"Extreme outliers found: {len(extreme_outliers)} records\")\n", "    print(f\"Final cleaned dataset: {len(cleaned_df)} records\")\n", "\n", "    print(f\"\\nUplift statistics (after cleaning):\")\n", "    print(f\"  Mean: {cleaned_df[uplift_column].mean():.2f}%\")\n", "    print(f\"  Median: {cleaned_df[uplift_column].median():.2f}%\")\n", "    print(f\"  Std: {cleaned_df[uplift_column].std():.2f}%\")\n", "    print(f\"  Max: {cleaned_df[uplift_column].max():.2f}%\")\n", "\n", "    return cleaned_df, extreme_outliers\n", "\n", "print(\"Running outlier removal for linear regression data\")\n", "print(\"=\" * 50)\n", "\n", "cleaned_data, removed_outliers = remove_extreme_uplift_outliers(data, outlier_threshold=50.0)\n", "\n", "if cleaned_data is not None:\n", "    print(f\"\\nOutlier removal completed.\")\n", "    print(f\"Cleaned data shape: {cleaned_data.shape}\")\n", "    print(f\"Removed {len(removed_outliers)} outliers\")\n", "else:\n", "    print(\"Outlier removal failed!\")\n", "    cleaned_data = data.copy()\n", "    print(f\"Using original data: {cleaned_data.shape}\")\n", "\n", "df = cleaned_data\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d555b6a0-fc2a-43d3-bfc2-c2c24f623496", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"<PERSON>lo\", \"Monaco\", \"Consolas\", \"Ubuntu Mono\", \"Source Code Pro\", monospace;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">Building Linear Regression with Features and Dummy Encoding\n", "============================================================\n", "Available columns in dataset:\n", "[&#39;ABI MS Promo Uplift - rel&#39;, &#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;, &#39;Avg Temp&#39;, &#39;ABI Coverage&#39;, &#39;ABI Depth&#39;, &#39;ABI Mechanic&#39;]\n", "Successfully loaded data: (837, 11)\n", "\n", "Target (y): (837,)\n", "Features (X): (837, 10)\n", "Target range: [0.37, 49.45], Mean: 3.41\n", "\n", "Implementing Dummy Encoding\n", "========================================\n", "Variables that need dummy encoding:\n", "ABI Mechanic values: LV           586\n", "Immediate    115\n", "FID           86\n", "No NIP        50\n", "Name: ABI Mechanic, dtype: int64\n", "ABI Depth values: 26%-30%    366\n", "21%-25%    356\n", "34%+        61\n", "&lt;20%        50\n", "31%-33%      4\n", "Name: ABI Depth, dtype: int64\n", "\n", "1. ABI Mechanic - Dummy encoding (dropping &#39;No NIP&#39; as base)...\n", "Dropped &#39;No NIP&#39; as base category\n", "ABI Mechanic dummy columns: [&#39;ABI_Mechanic_FID&#39;, &#39;ABI_Mechanic_Immediate&#39;, &#39;ABI_Mechanic_LV&#39;]\n", "\n", "2. ABI Depth - Dummy encoding (dropping &#39;&lt;20%&#39; as base)...\n", "Dropped &#39;ABI_Depth_&lt;20%&#39; as base category\n", "ABI Depth dummy columns: [&#39;ABI_Depth_21%-25%&#39;, &#39;ABI_Depth_26%-30%&#39;, &#39;ABI_Depth_31%-33%&#39;, &#39;ABI_Depth_34%+&#39;]\n", "\n", "3. Other variables (no encoding needed):\n", "Numerical: [&#39;Avg Temp&#39;, &#39;ABI Coverage&#39;]\n", "Binary categorical: [&#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;]\n", "  Same Week: [0, 1]\n", "  1 wk before: [0, 1]\n", "  2 wk before: [0, 1]\n", "  1 wk after: [0, 1]\n", "  2 wk after: [0, 1]\n", "  KSM: [0, 1]\n", "\n", "Dummy encoding completed\n", "- ABI Mechanic: 3 dummy variables\n", "- ABI Depth: 4 dummy variables\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">Building Linear Regression with Features and Dummy Encoding\n============================================================\nAvailable columns in dataset:\n[&#39;ABI MS Promo Uplift - rel&#39;, &#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;, &#39;Avg Temp&#39;, &#39;ABI Coverage&#39;, &#39;ABI Depth&#39;, &#39;ABI Mechanic&#39;]\nSuccessfully loaded data: (837, 11)\n\nTarget (y): (837,)\nFeatures (X): (837, 10)\nTarget range: [0.37, 49.45], Mean: 3.41\n\nImplementing Dummy Encoding\n========================================\nVariables that need dummy encoding:\nABI Mechanic values: LV           586\nImmediate    115\nFID           86\nNo NIP        50\nName: ABI Mechanic, dtype: int64\nABI Depth values: 26%-30%    366\n21%-25%    356\n34%+        61\n&lt;20%        50\n31%-33%      4\nName: ABI Depth, dtype: int64\n\n1. ABI Mechanic - Dummy encoding (dropping &#39;No NIP&#39; as base)...\nDropped &#39;No NIP&#39; as base category\nABI Mechanic dummy columns: [&#39;ABI_Mechanic_FID&#39;, &#39;ABI_Mechanic_Immediate&#39;, &#39;ABI_Mechanic_LV&#39;]\n\n2. ABI Depth - Dummy encoding (dropping &#39;&lt;20%&#39; as base)...\nDropped &#39;ABI_Depth_&lt;20%&#39; as base category\nABI Depth dummy columns: [&#39;ABI_Depth_21%-25%&#39;, &#39;ABI_Depth_26%-30%&#39;, &#39;ABI_Depth_31%-33%&#39;, &#39;ABI_Depth_34%+&#39;]\n\n3. Other variables (no encoding needed):\nNumerical: [&#39;Avg Temp&#39;, &#39;ABI Coverage&#39;]\nBinary categorical: [&#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;]\n  Same Week: [0, 1]\n  1 wk before: [0, 1]\n  2 wk before: [0, 1]\n  1 wk after: [0, 1]\n  2 wk after: [0, 1]\n  KSM: [0, 1]\n\nDummy encoding completed\n- ABI Mechanic: 3 dummy variables\n- ABI Depth: 4 dummy variables\n</div>", "datasetInfos": [], "metadata": {}, "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["print(\"Building Linear Regression with Features and Dummy Encoding\")\n", "print(\"=\" * 60)\n", "\n", "print(\"Available columns in dataset:\")\n", "print(df.columns.tolist())\n", "\n", "correct_columns = [\n", "    'ABI MS Promo Uplift - rel',\n", "    'Same Week',\n", "    '1 wk before',\n", "    '2 wk before',\n", "    '1 wk after',\n", "    '2 wk after',\n", "    'KSM',\n", "    'Avg Temp',\n", "    'ABI Coverage',\n", "    'ABI Depth',\n", "    'ABI Mechanic'\n", "]\n", "\n", "try:\n", "    model_data = df[correct_columns].copy()\n", "    model_data = model_data.dropna()\n", "    print(f\"Successfully loaded data: {model_data.shape}\")\n", "except KeyError as e:\n", "    print(f\"Column error: {e}\")\n", "    print(\"Available columns check:\")\n", "    available_cols = [col for col in correct_columns if col in df.columns]\n", "    missing_cols = [col for col in correct_columns if col not in df.columns]\n", "    print(f\"Available: {available_cols}\")\n", "    print(f\"Missing: {missing_cols}\")\n", "\n", "y = model_data['ABI MS Promo Uplift - rel']\n", "X = model_data.drop('ABI MS Promo Uplift - rel', axis=1)\n", "\n", "print(f\"\\nTarget (y): {y.shape}\")\n", "print(f\"Features (X): {X.shape}\")\n", "print(f\"Target range: [{y.min():.2f}, {y.max():.2f}], Mean: {y.mean():.2f}\")\n", "\n", "print(f\"\\nImplementing Dummy Encoding\")\n", "print(\"=\" * 40)\n", "\n", "print(\"Variables that need dummy encoding:\")\n", "print(f\"ABI Mechanic values: {X['ABI Mechanic'].value_counts()}\")\n", "print(f\"ABI Depth values: {X['ABI Depth'].value_counts()}\")\n", "\n", "print(f\"\\n1. ABI Mechanic - Dummy encoding (dropping 'No NIP' as base)...\")\n", "mechanic_dummies = pd.get_dummies(X['ABI Mechanic'], prefix='ABI_Mechanic', drop_first=False)\n", "\n", "if 'ABI_Mechanic_No NIP' in mechanic_dummies.columns:\n", "    mechanic_dummies = mechanic_dummies.drop('ABI_Mechanic_No NIP', axis=1)\n", "    print(\"Dropped 'No NIP' as base category\")\n", "else:\n", "    all_mechanic_cols = mechanic_dummies.columns.tolist()\n", "    print(f\"Available mechanic categories: {all_mechanic_cols}\")\n", "    dropped_col = all_mechanic_cols[0]\n", "    mechanic_dummies = mechanic_dummies.drop(dropped_col, axis=1)\n", "    print(f\"'No NIP' not found. Dropped '{dropped_col}' as base instead\")\n", "\n", "print(f\"ABI Mechanic dummy columns: {mechanic_dummies.columns.tolist()}\")\n", "\n", "print(f\"\\n2. ABI Depth - Dummy encoding (dropping '<20%' as base)...\")\n", "depth_dummies = pd.get_dummies(X['ABI Depth'], prefix='ABI_Depth', drop_first=False)\n", "\n", "base_dropped = False\n", "for possible_base in ['ABI_Depth_<20%', 'ABI_Depth_<20']:\n", "    if possible_base in depth_dummies.columns:\n", "        depth_dummies = depth_dummies.drop(possible_base, axis=1)\n", "        print(f\"Dropped '{possible_base}' as base category\")\n", "        base_dropped = True\n", "        break\n", "\n", "if not base_dropped:\n", "    all_depth_cols = depth_dummies.columns.tolist()\n", "    print(f\"Available depth categories: {all_depth_cols}\")\n", "    dropped_col = all_depth_cols[0]\n", "    depth_dummies = depth_dummies.drop(dropped_col, axis=1)\n", "    print(f\"'<20%' not found. Dropped '{dropped_col}' as base instead\")\n", "\n", "print(f\"ABI Depth dummy columns: {depth_dummies.columns.tolist()}\")\n", "\n", "numerical_vars = ['Avg Temp', 'ABI Coverage']\n", "binary_categorical = ['Same Week', '1 wk before', '2 wk before', '1 wk after', '2 wk after', 'KSM']\n", "\n", "print(f\"\\n3. Other variables (no encoding needed):\")\n", "print(f\"Numerical: {numerical_vars}\")\n", "print(f\"Binary categorical: {binary_categorical}\")\n", "\n", "for col in binary_categorical:\n", "    unique_vals = sorted(X[col].unique())\n", "    print(f\"  {col}: {unique_vals}\")\n", "\n", "print(f\"\\nDummy encoding completed\")\n", "print(f\"- ABI Mechanic: {len(mechanic_dummies.columns)} dummy variables\")\n", "print(f\"- ABI Depth: {len(depth_dummies.columns)} dummy variables\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6950fe5b-0fdf-4736-807d-8a33caed85fd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"<PERSON>lo\", \"Monaco\", \"Consolas\", \"Ubuntu Mono\", \"Source Code Pro\", monospace;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">Building Linear Regression Model with Dummy Encoding\n", "============================================================\n", "Working dataset: (837, 11)\n", "Target (y): (837,)\n", "Features (X): (837, 10)\n", "\n", "Implementing dummy encoding\n", "========================================\n", "\n", "1. ABI Mechanic - Dummy encoding (dropping &#39;No NIP&#39; as base)...\n", "ABI Mechanic values: LV           586\n", "Immediate    115\n", "FID           86\n", "No NIP        50\n", "Name: ABI Mechanic, dtype: int64\n", "Dropped &#39;No NIP&#39; as base category\n", "ABI Mechanic dummy columns: [&#39;ABI_Mechanic_FID&#39;, &#39;ABI_Mechanic_Immediate&#39;, &#39;ABI_Mechanic_LV&#39;]\n", "\n", "2. ABI Depth - Dummy encoding (dropping &#39;&lt;20%&#39; as base)...\n", "ABI Depth values: 26%-30%    366\n", "21%-25%    356\n", "34%+        61\n", "&lt;20%        50\n", "31%-33%      4\n", "Name: ABI Depth, dtype: int64\n", "Dropped &#39;ABI_Depth_&lt;20%&#39; as base category\n", "ABI Depth dummy columns: [&#39;ABI_Depth_21%-25%&#39;, &#39;ABI_Depth_26%-30%&#39;, &#39;ABI_Depth_31%-33%&#39;, &#39;ABI_Depth_34%+&#39;]\n", "\n", "3. Other variables (no encoding needed):\n", "Numerical: [&#39;Avg Temp&#39;, &#39;ABI Coverage&#39;]\n", "Binary categorical: [&#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;]\n", "\n", "Final feature matrix complete\n", "Final feature matrix shape: (837, 15)\n", "Total features: 15\n", "\n", "Training linear regression model\n", "========================================\n", "Training set: (753, 15)\n", "Test set:     (84, 15)\n", "\n", "Model performance:\n", "Training R²:  0.0663\n", "Test R²:      0.0317\n", "Training RMSE: 3.5357\n", "Test RMSE:     2.2431\n", "\n", "Linear regression model with dummy encoding complete\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">Building Linear Regression Model with Dummy Encoding\n============================================================\nWorking dataset: (837, 11)\nTarget (y): (837,)\nFeatures (X): (837, 10)\n\nImplementing dummy encoding\n========================================\n\n1. ABI Mechanic - Dummy encoding (dropping &#39;No NIP&#39; as base)...\nABI Mechanic values: LV           586\nImmediate    115\nFID           86\nNo NIP        50\nName: ABI Mechanic, dtype: int64\nDropped &#39;No NIP&#39; as base category\nABI Mechanic dummy columns: [&#39;ABI_Mechanic_FID&#39;, &#39;ABI_Mechanic_Immediate&#39;, &#39;ABI_Mechanic_LV&#39;]\n\n2. ABI Depth - Dummy encoding (dropping &#39;&lt;20%&#39; as base)...\nABI Depth values: 26%-30%    366\n21%-25%    356\n34%+        61\n&lt;20%        50\n31%-33%      4\nName: ABI Depth, dtype: int64\nDropped &#39;ABI_Depth_&lt;20%&#39; as base category\nABI Depth dummy columns: [&#39;ABI_Depth_21%-25%&#39;, &#39;ABI_Depth_26%-30%&#39;, &#39;ABI_Depth_31%-33%&#39;, &#39;ABI_Depth_34%+&#39;]\n\n3. Other variables (no encoding needed):\nNumerical: [&#39;Avg Temp&#39;, &#39;ABI Coverage&#39;]\nBinary categorical: [&#39;Same Week&#39;, &#39;1 wk before&#39;, &#39;2 wk before&#39;, &#39;1 wk after&#39;, &#39;2 wk after&#39;, &#39;KSM&#39;]\n\nFinal feature matrix complete\nFinal feature matrix shape: (837, 15)\nTotal features: 15\n\nTraining linear regression model\n========================================\nTraining set: (753, 15)\nTest set:     (84, 15)\n\nModel performance:\nTraining R²:  0.0663\nTest R²:      0.0317\nTraining RMSE: 3.5357\nTest RMSE:     2.2431\n\nLinear regression model with dummy encoding complete\n</div>", "datasetInfos": [], "metadata": {}, "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["print(\"Building Linear Regression Model with Dummy Encoding\")\n", "print(\"=\" * 60)\n", "\n", "correct_columns = [\n", "    'ABI MS Promo Uplift - rel',\n", "    'Same Week',\n", "    '1 wk before',\n", "    '2 wk before',\n", "    '1 wk after',\n", "    '2 wk after',\n", "    'KSM',\n", "    'Avg Temp',\n", "    'ABI Coverage',\n", "    'ABI Depth',\n", "    'ABI Mechanic'\n", "]\n", "\n", "working_data = df[correct_columns].copy().dropna()\n", "\n", "print(f\"Working dataset: {working_data.shape}\")\n", "\n", "y = working_data['ABI MS Promo Uplift - rel']\n", "X = working_data.drop('ABI MS Promo Uplift - rel', axis=1)\n", "\n", "print(f\"Target (y): {y.shape}\")\n", "print(f\"Features (X): {X.shape}\")\n", "\n", "print(f\"\\nImplementing dummy encoding\")\n", "print(\"=\" * 40)\n", "\n", "print(f\"\\n1. ABI Mechanic - Dummy encoding (dropping 'No NIP' as base)...\")\n", "print(f\"ABI Mechanic values: {X['ABI Mechanic'].value_counts()}\")\n", "\n", "mechanic_dummies = pd.get_dummies(X['ABI Mechanic'], prefix='ABI_Mechanic', drop_first=False)\n", "\n", "if 'ABI_Mechanic_No NIP' in mechanic_dummies.columns:\n", "    mechanic_dummies = mechanic_dummies.drop('ABI_Mechanic_No NIP', axis=1)\n", "    print(\"Dropped 'No NIP' as base category\")\n", "else:\n", "    dropped_col = mechanic_dummies.columns[0]\n", "    mechanic_dummies = mechanic_dummies.drop(dropped_col, axis=1)\n", "    print(f\"'No NIP' not found. Dropped '{dropped_col}' as base instead\")\n", "\n", "print(f\"ABI Mechanic dummy columns: {mechanic_dummies.columns.tolist()}\")\n", "\n", "print(f\"\\n2. ABI Depth - Dummy encoding (dropping '<20%' as base)...\")\n", "print(f\"ABI Depth values: {X['ABI Depth'].value_counts()}\")\n", "\n", "depth_dummies = pd.get_dummies(X['ABI Depth'], prefix='ABI_Depth', drop_first=False)\n", "\n", "base_dropped = False\n", "for possible_base in ['ABI_Depth_<20%', 'ABI_Depth_<20']:\n", "    if possible_base in depth_dummies.columns:\n", "        depth_dummies = depth_dummies.drop(possible_base, axis=1)\n", "        print(f\"Dropped '{possible_base}' as base category\")\n", "        base_dropped = True\n", "        break\n", "\n", "if not base_dropped:\n", "    dropped_col = depth_dummies.columns[0]\n", "    depth_dummies = depth_dummies.drop(dropped_col, axis=1)\n", "    print(f\"'<20%' not found. Dropped '{dropped_col}' as base instead\")\n", "\n", "print(f\"ABI Depth dummy columns: {depth_dummies.columns.tolist()}\")\n", "\n", "numerical_vars = ['Avg Temp', 'ABI Coverage']\n", "binary_categorical = ['Same Week', '1 wk before', '2 wk before', '1 wk after', '2 wk after', 'KSM']\n", "\n", "print(f\"\\n3. Other variables (no encoding needed):\")\n", "print(f\"Numerical: {numerical_vars}\")\n", "print(f\"Binary categorical: {binary_categorical}\")\n", "\n", "X_final = pd.concat([\n", "    X[numerical_vars],\n", "    X[binary_categorical],\n", "    mechanic_dummies,\n", "    depth_dummies\n", "], axis=1)\n", "\n", "print(f\"\\nFinal feature matrix complete\")\n", "print(f\"Final feature matrix shape: {X_final.shape}\")\n", "print(f\"Total features: {X_final.shape[1]}\")\n", "\n", "print(f\"\\nTraining linear regression model\")\n", "print(\"=\" * 40)\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_final, y, test_size=0.1, random_state=42\n", ")\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Test set:     {X_test.shape}\")\n", "\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train, y_train)\n", "\n", "y_train_pred = lr_model.predict(X_train)\n", "y_test_pred = lr_model.predict(X_test)\n", "\n", "train_r2 = r2_score(y_train, y_train_pred)\n", "test_r2 = r2_score(y_test, y_test_pred)\n", "train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))\n", "test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))\n", "\n", "print(f\"\\nModel performance:\")\n", "print(f\"Training R²:  {train_r2:.4f}\")\n", "print(f\"Test R²:      {test_r2:.4f}\")\n", "print(f\"Training RMSE: {train_rmse:.4f}\")\n", "print(f\"Test RMSE:     {test_rmse:.4f}\")\n", "\n", "print(f\"\\nLinear regression model with dummy encoding complete\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "049ca8c8-5e34-4fe5-ae8f-9a5bb5058b19", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"<PERSON>lo\", \"Monaco\", \"Consolas\", \"Ubuntu Mono\", \"Source Code Pro\", monospace;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">OLS Regression Analysis with StatsModels\n", "==================================================\n", "Data type checking:\n", "X_train dtypes:\n", "Avg Temp                  float64\n", "ABI Coverage              float64\n", "Same Week                   int64\n", "1 wk before                 int64\n", "2 wk before                 int64\n", "1 wk after                  int64\n", "2 wk after                  int64\n", "KSM                         int64\n", "ABI_Mechanic_FID            uint8\n", "ABI_Mechanic_Immediate      uint8\n", "ABI_Mechanic_LV             uint8\n", "ABI_Depth_21%-25%           uint8\n", "ABI_Depth_26%-30%           uint8\n", "ABI_Depth_31%-33%           uint8\n", "ABI_Depth_34%+              uint8\n", "dtype: object\n", "y_train dtype: float64\n", "\n", "Converting data types for StatsModels compatibility...\n", "All data converted to numeric:\n", "X_train_numeric dtypes: [dtype(&#39;float64&#39;)]\n", "y_train_numeric dtype: float64\n", "Features with constant term: (753, 16)\n", "Constant added dtypes: [dtype(&#39;float64&#39;)]\n", "\n", "Fitting OLS model...\n", "\n", "OLS Regression Results\n", "==================================================\n", "                                OLS Regression Results                               \n", "=====================================================================================\n", "Dep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.066\n", "Model:                                   OLS   Adj. R-squared:                  0.047\n", "Method:                        Least Squares   F-statistic:                     3.486\n", "Date:                       Wed, 25 Jun 2025   Prob (F-statistic):           8.38e-06\n", "Time:                               09:52:14   Log-Likelihood:                -2019.4\n", "No. Observations:                        753   AIC:                             4071.\n", "Df Residuals:                            737   BIC:                             4145.\n", "Df Model:                                 15                                         \n", "Covariance Type:                   nonrobust                                         \n", "==========================================================================================\n", "                             coef    std err          t      P&gt;|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------------------\n", "const                     -1.5466      0.859     -1.801      0.072      -3.233       0.140\n", "Avg Temp                   0.0302      0.023      1.322      0.187      -0.015       0.075\n", "ABI Coverage               3.3339      0.643      5.183      0.000       2.071       4.597\n", "Same Week                  1.3410      0.506      2.652      0.008       0.348       2.334\n", "1 wk before               -0.1770      0.405     -0.437      0.662      -0.972       0.618\n", "2 wk before               -0.2575      0.357     -0.721      0.471      -0.959       0.444\n", "1 wk after                 0.1745      0.420      0.415      0.678      -0.650       0.999\n", "2 wk after                -0.0099      0.325     -0.031      0.976      -0.648       0.628\n", "KSM                       -0.2424      0.305     -0.795      0.427      -0.841       0.356\n", "ABI_Mechanic_FID           1.9242      0.968      1.988      0.047       0.024       3.824\n", "ABI_Mechanic_Immediate     2.0492      0.944      2.172      0.030       0.197       3.901\n", "ABI_Mechanic_LV            1.9831      0.950      2.088      0.037       0.118       3.848\n", "ABI_Depth_21%-25%         -0.2584      0.950     -0.272      0.786      -2.124       1.607\n", "ABI_Depth_26%-30%         -0.3179      0.912     -0.349      0.728      -2.108       1.473\n", "ABI_Depth_31%-33%         -2.0523      2.045     -1.004      0.316      -6.067       1.962\n", "ABI_Depth_34%+            -0.8455      1.002     -0.844      0.399      -2.812       1.121\n", "==============================================================================\n", "Omnibus:                      850.206   <PERSON><PERSON><PERSON>-Watson:                   1.892\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            75921.119\n", "Skew:                           5.368   Prob(JB):                         0.00\n", "Kurtosis:                      51.005   Cond. No.                         318.\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\n", "Additional Model Diagnostics\n", "========================================\n", "OLS Test R²:       0.0317\n", "OLS Test RMSE:     2.2431\n", "OLS Test MAE:      1.7902\n", "\n", "Scikit-learn vs StatsModels comparison:\n", "Training R² - SKlearn: 0.0663 | StatsModels: 0.0663\n", "Test R² - SKlearn:     0.0317 | StatsModels: 0.0317\n", "\n", "Statistical Significance Summary\n", "========================================\n", "Variables with p-value &lt; 0.05 (statistically significant):\n", "  ABI Coverage              (p=0.0000) ***\n", "  Same Week                 (p=0.0082) **\n", "  ABI_Mechanic_FID          (p=0.0472) *\n", "  ABI_Mechanic_Immediate    (p=0.0302) *\n", "  ABI_Mechanic_LV           (p=0.0372) *\n", "\n", "Total significant variables: 5 out of 16\n", "\n", "Analysis complete\n", "F-statistic: 3.4861 (p-value: 0.0000)\n", "Overall model significance: Significant\n", "\n", "OLS regression summary exported to &#39;unclubbed_ols_regression_report.txt&#39;.\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">OLS Regression Analysis with StatsModels\n==================================================\nData type checking:\nX_train dtypes:\nAvg Temp                  float64\nABI Coverage              float64\nSame Week                   int64\n1 wk before                 int64\n2 wk before                 int64\n1 wk after                  int64\n2 wk after                  int64\nKSM                         int64\nABI_Mechanic_FID            uint8\nABI_Mechanic_Immediate      uint8\nABI_Mechanic_LV             uint8\nABI_Depth_21%-25%           uint8\nABI_Depth_26%-30%           uint8\nABI_Depth_31%-33%           uint8\nABI_Depth_34%+              uint8\ndtype: object\ny_train dtype: float64\n\nConverting data types for StatsModels compatibility...\nAll data converted to numeric:\nX_train_numeric dtypes: [dtype(&#39;float64&#39;)]\ny_train_numeric dtype: float64\nFeatures with constant term: (753, 16)\nConstant added dtypes: [dtype(&#39;float64&#39;)]\n\nFitting OLS model...\n\nOLS Regression Results\n==================================================\n                                OLS Regression Results                               \n=====================================================================================\nDep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.066\nModel:                                   OLS   Adj. R-squared:                  0.047\nMethod:                        Least Squares   F-statistic:                     3.486\nDate:                       Wed, 25 Jun 2025   Prob (F-statistic):           8.38e-06\nTime:                               09:52:14   Log-Likelihood:                -2019.4\nNo. Observations:                        753   AIC:                             4071.\nDf Residuals:                            737   BIC:                             4145.\nDf Model:                                 15                                         \nCovariance Type:                   nonrobust                                         \n==========================================================================================\n                             coef    std err          t      P&gt;|t|      [0.025      0.975]\n------------------------------------------------------------------------------------------\nconst                     -1.5466      0.859     -1.801      0.072      -3.233       0.140\nAvg Temp                   0.0302      0.023      1.322      0.187      -0.015       0.075\nABI Coverage               3.3339      0.643      5.183      0.000       2.071       4.597\nSame Week                  1.3410      0.506      2.652      0.008       0.348       2.334\n1 wk before               -0.1770      0.405     -0.437      0.662      -0.972       0.618\n2 wk before               -0.2575      0.357     -0.721      0.471      -0.959       0.444\n1 wk after                 0.1745      0.420      0.415      0.678      -0.650       0.999\n2 wk after                -0.0099      0.325     -0.031      0.976      -0.648       0.628\nKSM                       -0.2424      0.305     -0.795      0.427      -0.841       0.356\nABI_Mechanic_FID           1.9242      0.968      1.988      0.047       0.024       3.824\nABI_Mechanic_Immediate     2.0492      0.944      2.172      0.030       0.197       3.901\nABI_Mechanic_LV            1.9831      0.950      2.088      0.037       0.118       3.848\nABI_Depth_21%-25%         -0.2584      0.950     -0.272      0.786      -2.124       1.607\nABI_Depth_26%-30%         -0.3179      0.912     -0.349      0.728      -2.108       1.473\nABI_Depth_31%-33%         -2.0523      2.045     -1.004      0.316      -6.067       1.962\nABI_Depth_34%+            -0.8455      1.002     -0.844      0.399      -2.812       1.121\n==============================================================================\nOmnibus:                      850.206   Durbin-Watson:                   1.892\nProb(Omnibus):                  0.000   Jarque-Bera (JB):            75921.119\nSkew:                           5.368   Prob(JB):                         0.00\nKurtosis:                      51.005   Cond. No.                         318.\n==============================================================================\n\nNotes:\n[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n\nAdditional Model Diagnostics\n========================================\nOLS Test R²:       0.0317\nOLS Test RMSE:     2.2431\nOLS Test MAE:      1.7902\n\nScikit-learn vs StatsModels comparison:\nTraining R² - SKlearn: 0.0663 | StatsModels: 0.0663\nTest R² - SKlearn:     0.0317 | StatsModels: 0.0317\n\nStatistical Significance Summary\n========================================\nVariables with p-value &lt; 0.05 (statistically significant):\n  ABI Coverage              (p=0.0000) ***\n  Same Week                 (p=0.0082) **\n  ABI_Mechanic_FID          (p=0.0472) *\n  ABI_Mechanic_Immediate    (p=0.0302) *\n  ABI_Mechanic_LV           (p=0.0372) *\n\nTotal significant variables: 5 out of 16\n\nAnalysis complete\nF-statistic: 3.4861 (p-value: 0.0000)\nOverall model significance: Significant\n\nOLS regression summary exported to &#39;unclubbed_ols_regression_report.txt&#39;.\n</div>", "datasetInfos": [], "metadata": {}, "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["import statsmodels.api as sm\n", "from scipy import stats\n", "\n", "print(\"OLS Regression Analysis with StatsModels\")\n", "print(\"=\" * 50)\n", "\n", "print(\"Data type checking:\")\n", "print(f\"X_train dtypes:\\n{X_train.dtypes}\")\n", "print(f\"y_train dtype: {y_train.dtype}\")\n", "\n", "print(\"\\nConverting data types for StatsModels compatibility...\")\n", "\n", "X_train_numeric = X_train.astype(float)\n", "X_test_numeric = X_test.astype(float)\n", "y_train_numeric = y_train.astype(float)\n", "y_test_numeric = y_test.astype(float)\n", "\n", "print(f\"All data converted to numeric:\")\n", "print(f\"X_train_numeric dtypes: {X_train_numeric.dtypes.unique()}\")\n", "print(f\"y_train_numeric dtype: {y_train_numeric.dtype}\")\n", "\n", "X_train_with_const = sm.add_constant(X_train_numeric, prepend=True)\n", "X_test_with_const = sm.add_constant(X_test_numeric, prepend=True)\n", "\n", "print(f\"Features with constant term: {X_train_with_const.shape}\")\n", "print(f\"Constant added dtypes: {X_train_with_const.dtypes.unique()}\")\n", "\n", "print(\"\\nFitting OLS model...\")\n", "ols_model = sm.OLS(y_train_numeric, X_train_with_const).fit()\n", "\n", "print(f\"\\nOLS Regression Results\")\n", "print(\"=\" * 50)\n", "print(ols_model.summary())\n", "\n", "print(f\"\\nAdditional Model Diagnostics\")\n", "print(\"=\" * 40)\n", "\n", "y_test_pred_ols = ols_model.predict(X_test_with_const)\n", "\n", "ols_test_r2 = r2_score(y_test_numeric, y_test_pred_ols)\n", "ols_test_rmse = np.sqrt(mean_squared_error(y_test_numeric, y_test_pred_ols))\n", "ols_test_mae = mean_absolute_error(y_test_numeric, y_test_pred_ols)\n", "\n", "print(f\"OLS Test R²:       {ols_test_r2:.4f}\")\n", "print(f\"OLS Test RMSE:     {ols_test_rmse:.4f}\")\n", "print(f\"OLS Test MAE:      {ols_test_mae:.4f}\")\n", "\n", "print(f\"\\nScikit-learn vs StatsModels comparison:\")\n", "print(f\"Training R² - SKlearn: {train_r2:.4f} | StatsModels: {ols_model.rsquared:.4f}\")\n", "print(f\"Test R² - SKlearn:     {test_r2:.4f} | StatsModels: {ols_test_r2:.4f}\")\n", "\n", "print(f\"\\nStatistical Significance Summary\")\n", "print(\"=\" * 40)\n", "print(\"Variables with p-value < 0.05 (statistically significant):\")\n", "\n", "significant_vars = []\n", "for i, (coef, pval) in enumerate(zip(ols_model.params, ols_model.pvalues)):\n", "    var_name = ols_model.params.index[i]\n", "    if pval < 0.05:\n", "        significance = \"***\" if pval < 0.001 else \"**\" if pval < 0.01 else \"*\"\n", "        print(f\"  {var_name:25s} (p={pval:.4f}) {significance}\")\n", "        significant_vars.append(var_name)\n", "\n", "if len(significant_vars) == 0:\n", "    print(\"  No variables are statistically significant at alpha=0.05\")\n", "\n", "print(f\"\\nTotal significant variables: {len(significant_vars)} out of {len(ols_model.params)}\")\n", "\n", "print(f\"\\nAnalysis complete\")\n", "print(f\"F-statistic: {ols_model.fvalue:.4f} (p-value: {ols_model.f_pvalue:.4f})\")\n", "print(f\"Overall model significance: {'Significant' if ols_model.f_pvalue < 0.05 else 'Not significant'}\")\n", "\n", "import os\n", "\n", "file_path = \"/dbfs/FileStore/RevMan/unclubbed_ols_regression_report.txt\"\n", "if not os.path.exists(file_path):\n", "    with open(file_path, \"w\") as f:\n", "        f.write(\"\")\n", "\n", "with open(file_path, \"w\") as f:\n", "    f.write(ols_model.summary().as_text())\n", "with open(\"/dbfs/FileStore/RevMan/unclubbed_ols_regression_report.txt\", \"w\") as f:\n", "    f.write(ols_model.summary().as_text())\n", "\n", "print(\"\\nOLS regression summary exported to 'unclubbed_ols_regression_report.txt'.\")\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "Linear Regression V1", "widgets": {}}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 0}