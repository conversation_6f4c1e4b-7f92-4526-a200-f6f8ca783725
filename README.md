# Linear Regression Analysis - ABI MS Uplift Prediction

## Overview
This repository contains a comprehensive Linear Regression analysis for predicting **ABI MS Uplift - rel** using the specified features from the FinalADS.csv dataset.

## Files
- `linear_regression_analysis.ipynb`: Main Jupyter notebook with complete analysis
- `FinalADS.csv`: Input dataset
- `README.md`: This documentation file

## Model Specifications

### Target Variable
- **y**: ABI MS Promo Uplift - rel (Numerical)

### Features (x1-x10)
1. **x1**: Same Week (Categorical)
2. **x2**: 1 wk before (Categorical)
3. **x3**: 2 wk before (Categorical)
4. **x4**: 1 wk after (Categorical)
5. **x5**: 2 wk after (Categorical)
6. **x6**: KSM (Categorical)
7. **x7**: Avg Temp (Numerical)
8. **x8**: ABI Coverage (Numerical)
9. **x9**: ABI Depth (Categorical)
10. **x10**: ABI Mechanic (Categorical)

## Special Dummy Encoding
- **ABI Mechanic**: Uses "No NIP" as base category (if available)
- **ABI Depth**: Uses "<20" as base category (if available)
- **Other categorical variables**: Standard dummy encoding (drop first category)

## Notebook Contents
1. **Data Loading & Exploration**: Initial data examination
2. **Feature Selection**: Selecting only the required columns
3. **Data Preprocessing**: Proper dummy encoding with specified base categories
4. **Model Training**: Linear regression model training
5. **Model Evaluation**: Performance metrics and validation
6. **Feature Analysis**: Coefficient analysis and importance
7. **Visualizations**: Prediction plots, residual analysis
8. **Summary**: Key findings and recommendations

## Key Features
- Comprehensive error handling for missing base categories
- Detailed performance metrics (R², RMSE, MAE)
- Visual diagnostics and validation plots
- Feature importance analysis
- Professional formatting and documentation

## Usage
1. Ensure you have the required libraries installed:
   - pandas, numpy, matplotlib, seaborn, scikit-learn
2. Place the `FinalADS.csv` file in the same directory
3. Run the notebook cells sequentially

## Model Performance
The notebook will output detailed performance metrics including:
- Training and test R² scores
- RMSE and MAE values
- Feature coefficients and importance ranking
- Residual analysis plots

This analysis provides a solid foundation for understanding the relationships between the features and ABI MS Uplift performance. 